<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Interactive polymetric sequencer visualizer for the PicoMudras project with real-time parameter control and modern glass-morphism UI">
    <meta name="keywords" content="sequencer, polymetric, music, visualization, PicoMudras, interactive">
    <meta name="author" content="PicoMudras Project">
    <title>PicoMudras Polymetric Sequencer Visualizer</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/addons/p5.sound.min.js"></script>
</head>
<body>
    <div class="container">
        <header role="banner">
            <h1>PicoMudras Polymetric Sequencer</h1>
            <p>Interactive visualization of independent parameter tracks</p>
        </header>

        <main role="main">
            <section class="controls" aria-label="Transport and Tempo Controls">
                <div class="transport-controls" role="group" aria-label="Transport Controls">
                    <button id="playBtn" class="btn btn-primary" aria-label="Play or pause sequencer" aria-pressed="false">
                        Play
                    </button>
                    <button id="stopBtn" class="btn btn-secondary" aria-label="Stop sequencer">
                        Stop
                    </button>
                    <button id="resetBtn" class="btn btn-secondary" aria-label="Reset sequencer to beginning">
                        Reset
                    </button>
                </div>

                <div class="tempo-control" role="group" aria-label="Tempo Control">
                    <label for="tempoSlider">Tempo: <span id="tempoValue" aria-live="polite">55</span> BPM</label>
                    <input type="range" id="tempoSlider" min="22" max="200" value="120" class="slider"
                           aria-label="Adjust tempo from 22 to 200 BPM" aria-valuemin="22" aria-valuemax="200" aria-valuenow="55">
                </div>
            </section>
        
            <section class="sequencer-info" aria-label="Sequencer Status Information">
                <div class="info-panel" role="region" aria-labelledby="pattern-info-title">
                    <h3 id="pattern-info-title">Pattern Info</h3>
                    <div class="info-item">
                        <span>Current Step:</span>
                        <span id="currentStep" aria-live="polite" aria-label="Current step number">1</span>
                    </div>
                    <div class="info-item">
                        <span>Pattern Length:</span>
                        <span id="patternLength" aria-live="polite" aria-label="Total pattern length">16</span>
                    </div>
                    <div class="info-item">
                        <span>Full Cycle:</span>
                        <span id="fullCycle" aria-live="polite" aria-label="Full cycle length">16</span> steps
                    </div>
                </div>
            </section>
        
            <section class="parameter-info" aria-label="Parameter Track Length Information">
                <h3 id="track-lengths-title">Parameter Track Lengths</h3>
                <div class="track-lengths-display" role="group" aria-labelledby="track-lengths-title">
                    <div class="track-length-item">
                        <div class="track-color-indicator" style="background-color: #ff6b6b;"></div>
                        <span class="track-length-label">Note:</span>
                        <span id="noteSteps" class="track-length-value" aria-live="polite">16</span>
                    </div>
                    <div class="track-length-item">
                        <div class="track-color-indicator" style="background-color: #4ecdc4;"></div>
                        <span class="track-length-label">Velocity:</span>
                        <span id="velSteps" class="track-length-value" aria-live="polite">16</span>
                    </div>
                    <div class="track-length-item">
                        <div class="track-color-indicator" style="background-color: #45b7d1;"></div>
                        <span class="track-length-label">Filter:</span>
                        <span id="filterSteps" class="track-length-value" aria-live="polite">16</span>
                    </div>
                    <div class="track-length-item">
                        <div class="track-color-indicator" style="background-color: #96ceb4;"></div>
                        <span class="track-length-label">Attack:</span>
                        <span id="attackSteps" class="track-length-value" aria-live="polite">16</span>
                    </div>
                    <div class="track-length-item">
                        <div class="track-color-indicator" style="background-color: #feca57;"></div>
                        <span class="track-length-label">Decay:</span>
                        <span id="decaySteps" class="track-length-value" aria-live="polite">16</span>
                    </div>
                    <div class="track-length-item">
                        <div class="track-color-indicator" style="background-color: #ff9ff3;"></div>
                        <span class="track-length-label">Octave:</span>
                        <span id="octSteps" class="track-length-value" aria-live="polite">16</span>
                    </div>
                    <div class="track-length-item">
                        <div class="track-color-indicator" style="background-color: #54a0ff;"></div>
                        <span class="track-length-label">Gate Len:</span>
                        <span id="gateSizeSteps" class="track-length-value" aria-live="polite">16</span>
                    </div>
                    <div class="track-length-item">
                        <div class="track-color-indicator" style="background-color: #5f27cd;"></div>
                        <span class="track-length-label">Gate:</span>
                        <span id="gateSteps" class="track-length-value" aria-live="polite">16</span>
                    </div>
                    <div class="track-length-item">
                        <div class="track-color-indicator" style="background-color: #00d2d3;"></div>
                        <span class="track-length-label">Slide:</span>
                        <span id="slideSteps" class="track-length-value" aria-live="polite">16</span>
                    </div>
                </div>
                <div class="track-length-instructions" aria-live="polite" style="text-align: center; font-size: 0.75rem; color: var(--text-secondary); margin-top: var(--spacing-xs);">
                    Click on any step in a track to set its length
                </div>
            </section>
        
            <section class="visualization-container" aria-label="Sequencer Visualization">
                <div id="p5-container" role="img" aria-label="Interactive sequencer visualization showing parameter tracks and current playback position"></div>
            </section>

            <section class="pattern-presets" aria-label="Pattern Preset Controls">
                <h3 id="presets-title">Pattern Presets</h3>
                <div class="preset-buttons" role="group" aria-labelledby="presets-title">
                    <button class="btn btn-preset" onclick="loadPreset('basic')"
                            aria-label="Load basic 4/4 pattern preset">
                        Basic 4/4
                    </button>
                    <button class="btn btn-preset" onclick="loadPreset('polyrhythm')"
                            aria-label="Load polyrhythmic pattern preset">
                        Polyrhythm
                    </button>
                    <button class="btn btn-preset" onclick="loadPreset('complex')"
                            aria-label="Load complex polyrhythmic pattern preset">
                        Complex
                    </button>
                    <button class="btn btn-preset" onclick="loadPreset('random')"
                            aria-label="Load random pattern preset">
                        Random
                    </button>
                </div>
            </section>
        </main>

        <footer role="contentinfo" style="text-align: center; padding: 2rem 0; color: var(--text-muted); font-size: 0.9rem;">
            <p>PicoMudras Polymetric Sequencer Visualizer | Use keyboard shortcuts: Space (Play/Pause), R (Reset), 1-4 (Presets)</p>
        </footer>
    </div>

    <script src="sequencer.js"></script>
</body>
</html>