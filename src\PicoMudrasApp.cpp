#include "PicoMudrasApp.h"
#include "midi/MidiManager.h"
#include "GlobalVariables.h"

// External references
extern MidiNoteManager midiNoteManager;

// Static instance pointer for interrupt handlers
PicoMudrasApp* PicoMudrasApp::instance_ = nullptr;

// Global instance
PicoMudrasApp app;

// Static callback functions for C-style function pointers
static void matrixEventCallback(const MatrixButtonEvent &evt) {
    if (PicoMudrasApp::instance_) {
        PicoMudrasApp::instance_->onMatrixEvent(evt);
    }
}

static void sync24Callback(uint32_t tick) {
    if (PicoMudrasApp::instance_) {
        PicoMudrasApp::instance_->onSync24Callback(tick);
    }
}

static void clockStartCallback() {
    if (PicoMudrasApp::instance_) {
        PicoMudrasApp::instance_->onClockStart();
    }
}

static void clockStopCallback() {
    if (PicoMudrasApp::instance_) {
        PicoMudrasApp::instance_->onClockStop();
    }
}

static void stepCallback(uint32_t step) {
    if (PicoMudrasApp::instance_) {
        PicoMudrasApp::instance_->onStepCallback(step);
    }
}

static void outputPPQNCallback(uint32_t tick) {
    if (PicoMudrasApp::instance_) {
        PicoMudrasApp::instance_->onOutputPPQNCallback(tick);
    }
}

// Scale definitions moved to SynthEngine

PicoMudrasApp::PicoMudrasApp() 
    : serial_usb_midi_(raw_usb_midi_), usb_midi_(serial_usb_midi_) {
    instance_ = this;
}

PicoMudrasApp::~PicoMudrasApp() {
    instance_ = nullptr;
}

bool PicoMudrasApp::initialize() {
    Serial.println("[PicoMudrasApp] Initializing application...");

    // Initialize random seed
    randomSeed(analogRead(A0) + millis());

    // Initialize all subsystems
    initializeAudio();
    Serial.println("[INFO] Audio initialization complete");

    initializeSequencers();
    initializeMIDI();
    initializeClock();

    initializeSensors();
    Serial.println("[INFO] Sensors initialization complete");

    initializeUI();
    initializeInterrupts();

    Serial.println("[PicoMudrasApp] Application initialized successfully!");
    return true;
}


void PicoMudrasApp::setupCore0() {
    // Audio core setup
       
    // Initialize all subsystems
    Serial.println("[INFO] Audio initialization complete");
    
    audio_format_t audioFormat = {
        .sample_freq = static_cast<uint32_t>(SAMPLE_RATE),
        .format = AUDIO_BUFFER_FORMAT_PCM_S16,
        .channel_count = 2,
    };
    
    audio_i2s_config_t i2sConfig = {
        .data_pin = PICO_AUDIO_I2S_DATA_PIN,
        .clock_pin_base = PICO_AUDIO_I2S_CLOCK_PIN_BASE,
        .dma_channel = 0,
        .pio_sm = 0,
    };
    
    setupI2SAudio(&audioFormat, &i2sConfig);
    
        initializeAudio();

    Serial.println("[CORE0] Audio setup complete!");
}

void PicoMudrasApp::setupCore1() {
    // UI and control core setup
    delay(300);
    usb_midi_.begin(MIDI_CHANNEL_OMNI);
    delay(100);
    Serial.begin(115200);

    // Critical: Wait for Serial to be ready
    while (!Serial) {
        delay(10);
    }

    Serial.println("[CORE1] USB Serial initialized successfully!");
    Serial.print("[CORE1] Setup starting... ");

    ledMatrix_.begin(200);
    setupLEDMatrixFeedback();
    initLEDController();
    // Initialize sensors
    if (!distanceSensor.begin()) {
        Serial.println("[ERROR] Distance sensor initialization failed!");
    } else {
        Serial.println("Distance sensor initialized successfully");
    }
    
    if (!as5600Sensor.begin()) {
        Serial.println("[ERROR] AS5600 magnetic encoder initialization failed!");
    } else {
        Serial.println("AS5600 magnetic encoder initialized successfully");
    }
    
    initAS5600BaseValues();
    
    // Initialize touch interfaces
    if (!touchMatrix_.begin(0x5A)) {
        Serial.println("[ERROR] Touch matrix initialization failed!");
        touchMatrixPresent_ = false;
    } else {
        Serial.println("Touch matrix found and initialized");
        touchMatrixPresent_ = true;
    }
    
    if (!touchButtons_.begin(0x5D)) {
        Serial.println("[ERROR] Touch buttons initialization failed!");
        touchButtonsPresent_ = false;
    } else {
        Serial.println("Touch buttons found and initialized");
        touchButtonsPresent_ = true;
    }
    // Update global variables for backward compatibility
    touchButtonsPresent = touchButtonsPresent_;
    touchMatrixPresent = touchMatrixPresent_;

    initUIEventHandler(UIState_);
    Matrix_init(&touchMatrix_);
    Matrix_setEventHandler(matrixEventCallback);
    
    // Start sequencers
    seq1_.start();
    seq2_.start();
    setShuffleTemplate(0); // Initialize with default template

    // Set MIDI callbacks
    seq1_.setMidiNoteOffCallback(sendMidiNoteOff1);
    seq2_.setMidiNoteOffCallback(sendMidiNoteOff2);

    // Debug: Verify sequencer states after initialization
    Serial.print("[Init] Sequencer states after start - Seq1: ");
    Serial.print(seq1_.isRunning() ? "RUNNING" : "STOPPED");
    Serial.print(", Seq2: ");
    Serial.print(seq2_.isRunning() ? "RUNNING" : "STOPPED");
    Serial.print(", Global isClockRunning: ");
    Serial.println(isClockRunning ? "TRUE" : "FALSE");

    Serial.println("[CORE1] UI and control setup complete!");
}

void PicoMudrasApp::runCore0() {
    // Audio processing loop
    audio_buffer_t *buf = take_audio_buffer(producer_pool_, true);
    if (buf) {
        fillAudioBuffer(buf);
        give_audio_buffer(producer_pool_, buf);
    }
}

void PicoMudrasApp::runCore1() {
    // UI and control loop
    usb_midi_.read();

  
    unsigned long currentMillis = millis();
    
    // Update sensors
    updateSensors();
    
    // Handle touch events
    handleTouchButtons(UIState_, seq1_, seq2_);
    
    // Scan matrix buttons if present
    if (touchMatrixPresent_) {
        Matrix_scan();
    }
    
    // Process PPQN ticks
    processPPQNTicks();
    
    // Update UI at regular intervals
    if (currentMillis - previousMillis_ > 0) {
        previousMillis_ = currentMillis;
        updateUI();
        
        if (UIState_.selectedStepForEdit != -1) {
            updateParametersForStep(UIState_.selectedStepForEdit);
        }
    }
}

void PicoMudrasApp::initializeAudio() {
    // Initialize audio system
    Serial.println("[Audio] Initializing audio system...");
    
    // Initialize the synthesis engine
    synthEngine_.initialize(SAMPLE_RATE);
    
    Serial.println("[Audio] Audio system initialized");
}

// initOscillators method moved to SynthEngine

void PicoMudrasApp::initializeSequencers() {
    Serial.println("[Sequencer] Initializing sequencers...");

    // Sequencers are initialized by their constructors
    // Set up any additional configuration here

    Serial.println("[Sequencer] Sequencers initialized");
}

void PicoMudrasApp::initializeMIDI() {
    Serial.println("[MIDI] Initializing MIDI system...");

    usb_midi_.begin(MIDI_CHANNEL_OMNI);

    Serial.println("[MIDI] MIDI system initialized");
}

bool PicoMudrasApp::initializeSensors() {
    Serial.println("[Sensors] Initializing sensors...");

    bool allSuccess = true;

    // Distance sensor initialization is handled in setupCore1
    // AS5600 sensor initialization is handled in setupCore1

    Serial.println("[Sensors] Sensors initialization complete");
    return allSuccess;
}

void PicoMudrasApp::initializeUI() {
    Serial.println("[UI] Initializing UI system...");

    // UI initialization is handled in setupCore1

    Serial.println("[UI] UI system initialized");
}

void PicoMudrasApp::initializeInterrupts() {
    pinMode(TOUCH_BUTTONS_IRQ_PIN, INPUT_PULLUP);
    attachInterrupt(digitalPinToInterrupt(TOUCH_BUTTONS_IRQ_PIN),
                   touchButtonsInterruptHandler, FALLING);
}


void PicoMudrasApp::initializeClock() {
    Serial.println("[Clock] Initializing clock system...");

    uClock.init();
    uClock.setOnSync24(sync24Callback);
    uClock.setOnClockStart(clockStartCallback);
    uClock.setOnClockStop(clockStopCallback);
    uClock.setOutputPPQN(uClock.PPQN_480);
    uClock.setOnStep(stepCallback);
    uClock.setOnOutputPPQN(outputPPQNCallback);
    uClock.setTempo(90);
    uClock.start();

    // Ensure global clock state is synchronized with uClock
    isClockRunning = true;
    isClockRunning_ = true;

    
}



void PicoMudrasApp::updateSensors() {
    // Update distance sensor
    distanceSensor.update();
    
    // Update global mm variable for backward compatibility
    int rawValue = distanceSensor.getRawValue();
    // Clamp sensor error (-1) to 0 to prevent unsigned wrap/underflow in mapping logic
    if (rawValue == -1) {
        rawValue = 0;
    }
    if (rawValue >= MIN_HEIGHT && rawValue <= MAX_HEIGHT) {
        mm_ = rawValue - MIN_HEIGHT;
    } else {
        mm_ = 0; // Invalid or out-of-range reading
    }
    
    // Update AS5600 sensor
    as5600Sensor.update();
    updateAS5600BaseValues(UIState_);
}

void PicoMudrasApp::updateUI() {
    updateStepLEDs(ledMatrix_, seq1_, seq2_, UIState_, mm_);
    updateControlLEDs(ledMatrix_, UIState_, currentShuffleTemplate_);
    ledMatrix_.show();
}

void PicoMudrasApp::processPPQNTicks() {
    static uint16_t globalTickCounter = 0;
    
    while (ppqnTicksPending > 0) {
        ppqnTicksPending--;
        globalTickCounter++;
        
        // Update MIDI note manager timing
        midiNoteManager.updateTiming(globalTickCounter);
        
        // Process sequencer note duration timing
        seq1_.tickNoteDuration(&voiceState1_);
        seq2_.tickNoteDuration(&voiceState2_);
        
        // Process gate timers
        gateTimer1_.tick();
        if (gateTimer1_.isExpired() && gate1_) {
            gate1_ = false;
        }
        
        // Safety mechanism for gate 1
        if (gate1_ && gateTimer1_.totalTicksProcessed > 960) {
            gate1_ = false;
            gateTimer1_.stop();
            midiNoteManager.setGateState(0, false);
        }
        
        gateTimer2_.tick();
        if (gateTimer2_.isExpired() && gate2_) {
            gate2_ = false;
        }
        
        // Safety mechanism for gate 2
        if (gate2_ && gateTimer2_.totalTicksProcessed > 960) {
            gate2_ = false;
            gateTimer2_.stop();
            midiNoteManager.setGateState(1, false);
        }
    }
}

// Event handlers
void PicoMudrasApp::onStepCallback(uint32_t uClockCurrentStep) {
    // Update current step for LED display
    currentSequencerStep_ = uClockCurrentStep;

    // Debug: Track step callbacks (only log every 16 steps to avoid spam)
    static uint32_t lastLoggedStep = 0;
    if (uClockCurrentStep % 16 == 0 && uClockCurrentStep != lastLoggedStep) {
        Serial.print("[Clock] Step callback: ");
        Serial.print(uClockCurrentStep);
        Serial.print(", Seq1 running: ");
        Serial.print(seq1_.isRunning() ? "YES" : "NO");
        Serial.print(", Seq2 running: ");
        Serial.println(seq2_.isRunning() ? "YES" : "NO");
        lastLoggedStep = uClockCurrentStep;
    }

   // Use existing LFO LED waveform values for LED feedback
    float lfo1Value = lfo1LEDWaveformValue_;
    float lfo2Value = lfo2LEDWaveformValue_;

    // Advance both sequencers
    seq1_.advanceStep(uClockCurrentStep, mm_, UIState_, &voiceState1_, lfo1Value, lfo2Value);
    seq2_.advanceStep(uClockCurrentStep, mm_, UIState_, &voiceState2_, lfo1Value, lfo2Value);

    // Update LED matrix to show current step
    resetStepsLightsFlag_ = true;
}

void PicoMudrasApp::onMatrixEvent(const MatrixButtonEvent &evt) {
    // Forward to the existing matrix event handler with all required parameters
    matrixEventHandler(evt, UIState_, seq1_, seq2_, midiNoteManager);
}

void PicoMudrasApp::onTouchEvent(uint8_t buttonIndex, bool pressed) {
    // This function is not called directly anymore.
    // The logic is now in handleTouchButtons, which is called from runCore1.
}

// Clock callbacks
void PicoMudrasApp::onSync24Callback(uint32_t tick) {
    usb_midi_.sendRealTime(midi::Clock);
}

void PicoMudrasApp::onClockStart() {
    Serial.println("[uClock] onClockStart()");
    usb_midi_.sendRealTime(midi::Start);
    seq1_.start();
    seq2_.start();

    // Update global clock state for UI synchronization
    isClockRunning = true;
    isClockRunning_ = true;

    Serial.println("[Clock] Sequencers started - clock is now running");
}

void PicoMudrasApp::onClockStop() {
    Serial.println("[uClock] onClockStop()");
    usb_midi_.sendRealTime(midi::Stop);
    seq1_.stop();
    seq2_.stop();

    // Update global clock state for UI synchronization
    isClockRunning = false;
    isClockRunning_ = false;

    Serial.println("[Clock] Sequencers stopped - clock is now stopped");
}

/**
 * @brief Callback invoked by the clock system at every PPQN (Pulses Per Quarter Note) tick.
 *
 * This method is called at high frequency by the uClock engine to handle all timing-critical
 * sequencer and gate events that must be processed on every MIDI clock subdivision.
 *
 * It delegates actual tick processing to processPPQNTicks(), which updates MIDI note timing,
 * sequencer note durations, and manages gate timers and safety mechanisms.
 *
 * Called automatically—do not invoke directly.
 *
 * See processPPQNTicks() for detailed tick handling logic.
 */
void PicoMudrasApp::onOutputPPQNCallback(uint32_t tick) {
    processPPQNTicks();
}

// Utility functions
float PicoMudrasApp::calculateFilterFrequency(float filterValue) {
    // Delegate to synthesis engine
    return synthEngine_.calculateFilterFrequency(filterValue);
}

void PicoMudrasApp::applyEnvelopeParameters(const VoiceState &state, daisysp::Adsr &env, int voiceNum) {
    // Delegate to synthesis engine
    synthEngine_.applyEnvelopeParameters(state, env, voiceNum);
}

float PicoMudrasApp::delayTimeSmoothing(float currentDelay, float targetDelay, float slewRate) {
    // Delegate to synthesis engine
    return synthEngine_.delayTimeSmoothing(currentDelay, targetDelay, slewRate);
}

/* ----------------------------------------------------------
 * Set Shuffle Template
 * Selects a shuffle/groove template by index and updates the
 * currentShuffleTemplate_ member. The actual tick offsets are
 * referenced from shuffleTemplates[currentShuffleTemplate_]
 * elsewhere in the codebase.
 * ---------------------------------------------------------- */
void PicoMudrasApp::setShuffleTemplate(int index) {
    // Clamp index to valid range
    if (index < 0) {
        index = 0;
    } else if (index >= NUM_SHUFFLE_TEMPLATES) {
        index = NUM_SHUFFLE_TEMPLATES - 1;
    }
    currentShuffleTemplate_ = index;
    // No further action required; sequencers reference the template as needed
}

/* ----------------------------------------------------------
 * Update Parameters for Step Editing
 * Ports and adapts the original global logic to class context.
 * Updates step parameters based on held parameter buttons and
 * normalized sensor value. Provides immediate audio feedback.
 * ---------------------------------------------------------- */
void PicoMudrasApp::updateParametersForStep(uint8_t stepToUpdate) {
    if (stepToUpdate >= SEQUENCER_MAX_STEPS)
        return;

    Sequencer& activeSeq = UIState_.isVoice2Mode ? seq2_ : seq1_;

    // Normalize sensor value (distance in mm)
    float normalized_mm_value = 0.0f;
    if (MAX_HEIGHT > 0) {
        normalized_mm_value = static_cast<float>(mm_) / static_cast<float>(MAX_HEIGHT);
    }
    normalized_mm_value = std::max(0.0f, std::min(normalized_mm_value, 1.0f));

    bool parametersWereUpdated = false;
    for (size_t i = 0; i < static_cast<size_t>(ParamId::Count); ++i) {
        if (UIState_.parameterButtonHeld[i]) {
            ParamId paramId = static_cast<ParamId>(i);
            float valueToSet = mapNormalizedValueToParamRange(paramId, normalized_mm_value);
            activeSeq.setStepParameterValue(paramId, stepToUpdate, valueToSet);
            parametersWereUpdated = true;
            // Debug print (optional): can be enabled for troubleshooting
            // Serial.printf("  -> Set %s to %f\n", CORE_PARAMETERS[i].name, valueToSet);
        }
    }

    // Provide immediate audio feedback when recording parameters to current step
    if (parametersWereUpdated) {
        synthEngine_.updateVoiceState(voiceState1_, voiceState2_);
    }
}

void PicoMudrasApp::fillAudioBuffer(audio_buffer_t *buffer) {
    // Update synthesis engine with current voice states
    synthEngine_.updateVoiceState(voiceState1_, voiceState2_);
    synthEngine_.updateGlobalParams(globalParams_);
    
    // Update gate states
    synthEngine_.setGate(0, gate1_);
    synthEngine_.setGate(1, gate2_);
    
    // Process audio through synthesis engine
    synthEngine_.processAudio(buffer);
}

void PicoMudrasApp::setupI2SAudio(audio_format_t *audioFormat, audio_i2s_config_t *i2sConfig) {
    // Implementation moved from global function
    // This will be implemented based on the original function
}

// Static interrupt handlers
void PicoMudrasApp::touchInterruptHandler() {
    if (instance_) {
        instance_->touchFlag_ = true;
    }
}

void PicoMudrasApp::touchButtonsInterruptHandler() {
    if (instance_) {
        instance_->touchButtonsInterruptFlag_ = true;
    }
}

void PicoMudrasApp::shutdown() {
    Serial.println("[PicoMudrasApp] Shutting down application...");
    
    // Stop clock
    uClock.stop();
    
    // Stop sequencers
    seq1_.stop();
    seq2_.stop();
    
    Serial.println("[PicoMudrasApp] Application shutdown complete");
}