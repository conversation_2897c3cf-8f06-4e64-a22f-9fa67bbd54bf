#ifndef PICO_MUDRAS_APP_H
#define PICO_MUDRAS_APP_H

#include "includes.h"
#include "audio/SynthEngine.h"
#include <FastLED.h>
#include <Melopero_VL53L1X.h>
#include <Adafruit_MPR121.h>
#include <MIDI.h>
#include <Adafruit_TinyUSB.h>
#include <Wire.h>
#include <cmath>
#include <cstdint>
#include <uClock.h>

/**
 * @brief Main application class that encapsulates all global state and logic
 * 
 * This class serves as the central coordinator for the PicoMudras sequencer,
 * managing all subsystems including audio, MIDI, sensors, UI, and sequencing.
 * It replaces the global variables and functions scattered throughout the main file.
 */
class PicoMudrasApp {
public:
    // Constructor and destructor
    PicoMudrasApp();
    ~PicoMudrasApp();

    // Main application lifecycle
    bool initialize();
    void setupCore0();  // Audio core setup
    void setupCore1();  // UI and control core setup
    void runCore0();    // Audio processing loop
    void runCore1();    // UI and control loop
    void shutdown();

    // Audio system management
    void initializeAudio();
    void fillAudioBuffer(audio_buffer_t *buffer);
    
    // Sequencer management
    void initializeSequencers();
    void setShuffleTemplate(int index);
    void updateParametersForStep(uint8_t stepToUpdate);
    
    // MIDI and clock management
    void initializeMIDI();
    void initializeClock();
    
    // Sensor management
    bool initializeSensors();
    void updateSensors();
    
    // UI management
    void initializeUI();
    void updateUI();
    
    // Event handlers
    void onStepCallback(uint32_t uClockCurrentStep);
    void onMatrixEvent(const MatrixButtonEvent &evt);
    void onTouchEvent(uint8_t buttonIndex, bool pressed);
    
    // Clock callbacks
    void onSync24Callback(uint32_t tick);
    void onClockStart();
    void onClockStop();
    void onOutputPPQNCallback(uint32_t tick);
    
    // Utility functions
    float calculateFilterFrequency(float filterValue);
    void applyEnvelopeParameters(const VoiceState &state, daisysp::Adsr &env, int voiceNum);
    float delayTimeSmoothing(float currentDelay, float targetDelay, float slewRate);
    
    // Getters for accessing internal state (for backward compatibility)
    UIState& getUIState() { return UIState_; }
    GlobalParams& getGlobalParams() { return globalParams_; }
    Sequencer& getSequencer1() { return seq1_; }
    Sequencer& getSequencer2() { return seq2_; }
    LEDMatrix& getLEDMatrix() { return ledMatrix_; }
    Adafruit_MPR121& getTouchMatrix() { return touchMatrix_; }
    Adafruit_MPR121& getTouchButtons() { return touchButtons_; }
    midi::MidiInterface<midi::SerialMIDI<Adafruit_USBD_MIDI>>& getUSBMidi() { return usb_midi_; }
    bool getTouchButtonsPresent() const { return touchButtonsPresent_; }
    bool getTouchMatrixPresent() const { return touchMatrixPresent_; }
    uint32_t getCurrentSequencerStep() const { return currentSequencerStep_; }
    
private:
    // Core state objects
    UIState UIState_;
    GlobalParams globalParams_;
    Sequencer seq1_;  // Channel 1 sequencer
    Sequencer seq2_;  // Channel 2 sequencer
    LEDMatrix ledMatrix_;
    
    // MIDI and communication
    Adafruit_USBD_MIDI raw_usb_midi_;
    midi::SerialMIDI<Adafruit_USBD_MIDI> serial_usb_midi_;
    midi::MidiInterface<midi::SerialMIDI<Adafruit_USBD_MIDI>> usb_midi_;
    Adafruit_MPR121 touchMatrix_;
    Adafruit_MPR121 touchButtons_;
    
    // Audio synthesis engine
    SynthEngine synthEngine_;
    
    // Voice and timing state
    VoiceState voiceState1_;
    VoiceState voiceState2_;
    GateTimer gateTimer1_;
    GateTimer gateTimer2_;
    
    // Audio parameters (moved to SynthEngine, keeping minimal state here)
    float lfo1LEDWaveformValue_ = 0.0f;
    float lfo2LEDWaveformValue_ = 0.0f;
    
    // Control and state variables
    volatile bool gate1_ = false;
    volatile bool gate2_ = false;
    volatile uint8_t currentSequencerStep_ = 0;
    volatile bool touchFlag_ = false;
    volatile bool touchButtonsInterruptFlag_ = false;
    volatile uint32_t ppqnTicksPending = 0;
    
    int currentShuffleTemplate_ = 0;
    int raw_mm_ = 0;
    int mm_ = 0;
    bool isClockRunning_ = true;
    unsigned long previousMillis_ = 0;
    bool resetStepsLightsFlag_ = true;
    
    // Hardware configuration
    bool touchButtonsPresent_ = true;
    bool touchMatrixPresent_ = false;
    
    // Audio system
    audio_buffer_pool_t *producer_pool_ = nullptr;
    
    // Constants now defined in HardwareConfig.h
    static constexpr float SAMPLE_RATE = AUDIO_SAMPLE_RATE;
    
    // Hardware pin definitions now in HardwareConfig.h
    static constexpr int MAX_HEIGHT = DISTANCE_SENSOR_MAX_HEIGHT;
    static constexpr int MIN_HEIGHT = DISTANCE_SENSOR_MIN_HEIGHT;
    static constexpr int PIN_TOUCH_IRQ = TOUCH_MATRIX_IRQ_PIN;
    static constexpr uint8_t PICO_AUDIO_I2S_DATA_PIN = AUDIO_I2S_DATA_PIN;
    static constexpr uint8_t PICO_AUDIO_I2S_CLOCK_PIN_BASE = AUDIO_I2S_CLOCK_PIN_BASE;
    static constexpr int IRQ_PIN = LED_MATRIX_DATA_PIN;
    static constexpr int MAX_MIDI_NOTES = MIDI_MAX_NOTES;
    static constexpr int NUM_AUDIO_BUFFERS = AUDIO_NUM_BUFFERS;
    static constexpr int SAMPLES_PER_BUFFER = AUDIO_SAMPLES_PER_BUFFER;
    // TOUCH_BUTTONS_IRQ_PIN is defined in HardwareConfig.h
    
    // Audio constants now in HardwareConfig.h
    static constexpr float INT16_MAX_AS_FLOAT = AUDIO_INT16_MAX_AS_FLOAT;
    static constexpr float INT16_MIN_AS_FLOAT = AUDIO_INT16_MIN_AS_FLOAT;
    
    // Private helper methods
    void setupI2SAudio(audio_format_t *audioFormat, audio_i2s_config_t *i2sConfig);
    void initializeInterrupts();
    void processAudioEffects(float &sample1, float &sample2);
    void updateFrequencySlewing();
    void processPPQNTicks();
    
    // Interrupt handlers (static to interface with C-style interrupts)
    static void touchInterruptHandler();
    static void touchButtonsInterruptHandler();
    
    // Static instance pointer for interrupt handlers
public:
    static PicoMudrasApp* instance_;
private:
};

// Global instance declaration (defined in .cpp file)
extern PicoMudrasApp app;

#endif // PICO_MUDRAS_APP_H