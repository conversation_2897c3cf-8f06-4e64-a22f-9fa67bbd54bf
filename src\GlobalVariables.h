#ifndef GLOBAL_VARIABLES_H
#define GLOBAL_VARIABLES_H

#include "PicoMudrasApp.h"
#include "HardwareConfig.h"
#include "sequencer/SequencerDefs.h"
#include "ui/UIState.h"
#include "sequencer/Sequencer.h"
#include <Adafruit_MPR121.h>
#include <MIDI.h>
#include <Adafruit_TinyUSB.h>

/**
 * @file GlobalVariables.h
 * @brief Global variable declarations for backward compatibility
 * 
 * This file provides extern declarations for global variables that are
 * accessed by various modules. These variables are actually members of
 * the PicoMudrasApp class, but this file provides global access for
 * backward compatibility during the refactoring process.
 */

// =============================================================================
// GLOBAL APPLICATION INSTANCE
// =============================================================================
extern PicoMudrasApp app;

// =============================================================================
// GLOBAL CONSTANTS
// =============================================================================
#define MAX_DELAY_SAMPLES AUDIO_MAX_DELAY_SAMPLES

// Macros have been removed to avoid confusion and compilation issues.
// Please use the global 'app' object directly to access its members, for example:
// app.getUIState(), app.getGlobalParams(), etc.

// =============================================================================
// GLOBAL STATE VARIABLES
// =============================================================================
// These need to be properly declared as extern and defined somewhere

extern float delayTarget;
extern float feedbackAmmount;

// Clock state variable
extern bool isClockRunning;
extern uint8_t currentScale;

// =============================================================================
// TOUCH SENSOR PRESENCE VARIABLES
// =============================================================================
// Global access to touch sensor presence flags
extern bool touchButtonsPresent;
extern bool touchMatrixPresent;

// =============================================================================
// ACCESSOR FUNCTIONS
// =============================================================================
// Functions to access PicoMudrasApp member variables
bool getTouchButtonsPresent();
bool getTouchMatrixPresent();

#endif // GLOBAL_VARIABLES_H